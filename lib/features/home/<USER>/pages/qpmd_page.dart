import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/config/themes/app_typography.dart';
import 'package:storetrack_app/config/routes/app_router.gr.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';
import 'package:storetrack_app/features/home/<USER>/services/photo_service.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/date_picker_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/text_field_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/counter_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/dropdown_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/checkbox_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/multi_select_widget.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/measurement_widgets/radio_button_widget.dart';
import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';

@RoutePage()
class QPMDPage extends StatefulWidget {
  final Question? question;
  final QuestionPart? questionPart;
  final num? taskId;
  final num? formId;

  const QPMDPage({
    super.key,
    this.question,
    this.questionPart,
    this.taskId,
    this.formId,
  });

  @override
  State<QPMDPage> createState() => _QPMDPageState();
}

class _QPMDPageState extends State<QPMDPage> {
  // State management for different measurement types
  final Map<num, dynamic> _measurementValues = {};

  // State management for validation errors
  final Map<num, String?> _validationErrors = {};

  // State management for widget visibility based on conditions
  final Map<num, bool> _widgetVisibility = {};

  // Photo service for loading and managing photos
  late final PhotoService _photoService;

  // State management for photos per measurement
  final Map<num, List<String>> _measurementPhotos = {};

  @override
  void initState() {
    super.initState();
    _photoService = sl<PhotoService>();
    _initializeMeasurementValues();
    _loadSavedQuestionAnswers();
    _loadSavedPhotos();
  }

  void _initializeMeasurementValues() {
    if (widget.question?.measurements != null) {
      for (final measurement in widget.question!.measurements!) {
        if (measurement.measurementId != null) {
          // Initialize widget visibility based on conditional logic
          _widgetVisibility[measurement.measurementId!] =
              _getInitialVisibility(measurement);

          // Initialize with default values based on measurement type
          switch (measurement.measurementTypeId) {
            case 9: // Date picker
              _measurementValues[measurement.measurementId!] = null;
              break;
            case 1: // Text field
            case 2: // Text field
              _measurementValues[measurement.measurementId!] = '';
              break;
            case 7: // Counter
              _measurementValues[measurement.measurementId!] = 0;
              break;
            case 4: // Dropdown
            case 5: // Dropdown
              _measurementValues[measurement.measurementId!] = null;
              break;
            case 3: // Checkbox
              _measurementValues[measurement.measurementId!] = false;
              break;
            case 6: // Multi-select
              _measurementValues[measurement.measurementId!] = <String>[];
              break;
            default:
              _measurementValues[measurement.measurementId!] = null;
          }
        }
      }

      // Process initial conditional logic for any pre-selected dropdown values
      _processInitialConditionalLogic();
    }
  }

  /// Determine initial visibility for a measurement widget
  /// Widgets that are targets of conditional actions should be hidden by default
  bool _getInitialVisibility(Measurement measurement) {
    if (measurement.measurementId == null) return true;

    // Check if this measurement is a target of any conditional action
    final measurements = widget.question?.measurements ?? [];

    for (final otherMeasurement in measurements) {
      if (otherMeasurement.measurementId == measurement.measurementId) continue;

      // Check measurement_conditions array
      if (otherMeasurement.measurementConditions != null) {
        for (final condition in otherMeasurement.measurementConditions!) {
          if (condition.actionMeasurementId == measurement.measurementId) {
            // This measurement is a target of conditional logic
            // Hide it by default if the action is 'appear'
            if (condition.action?.toLowerCase() == 'appear') {
              return false;
            }
          }
        }
      }

      // Check measurement_conditions_multiple array
      if (otherMeasurement.measurementConditionsMultiple != null) {
        for (final condition
            in otherMeasurement.measurementConditionsMultiple!) {
          if (condition.actionMeasurementId == measurement.measurementId) {
            // This measurement is a target of conditional logic
            // Hide it by default if the action is 'appear'
            if (condition.action?.toLowerCase() == 'appear') {
              return false;
            }
          }
        }
      }
    }

    // Default to visible if not a target of conditional logic
    return true;
  }

  /// Process initial conditional logic for any pre-selected values
  void _processInitialConditionalLogic() {
    final measurements = widget.question?.measurements ?? [];

    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      // Check if this widget can trigger conditional logic and has a pre-selected value
      if (_canTriggerConditionalLogic(measurement)) {
        final value = _measurementValues[measurement.measurementId!];
        if (value != null &&
            !_isValueEmpty(value, measurement.measurementTypeId)) {
          _processConditionalLogic(measurement, value);
        }
      }
    }
  }

  /// Load saved photos for measurements that have photo requirements
  Future<void> _loadSavedPhotos() async {
    if (widget.taskId == null || widget.question?.questionId == null) {
      logger('TaskId or QuestionId is null, cannot load saved photos');
      return;
    }

    try {
      // Clear existing photo data
      _measurementPhotos.clear();

      final taskId = widget.taskId!.toInt();
      final questionId = widget.question!.questionId!.toInt();
      final questionPartId = widget.questionPart?.questionpartId?.toInt();

      final savedPhotos = await _photoService.getPhotosFromTask(
        taskId: taskId,
        folderId: null,
      );

      // Filter photos for this question and level 3 (photo_tags_three)
      final filteredPhotos = savedPhotos.where((photo) {
        return photo.questionId == questionId &&
            photo.questionpartId == questionPartId &&
            photo.combineTypeId == 3; // Level 3 for photo_tags_three
      }).toList();

      if (filteredPhotos.isNotEmpty && mounted) {
        setState(() {
          // Group photos by measurement ID
          for (final photo in filteredPhotos) {
            if (photo.measurementId != null && photo.photoUrl != null) {
              final measurementId = photo.measurementId!;
              if (!_measurementPhotos.containsKey(measurementId)) {
                _measurementPhotos[measurementId] = [];
              }
              _measurementPhotos[measurementId]!.add(photo.photoUrl!);
            }
          }
        });
      }

      logger('Loaded ${filteredPhotos.length} photos for QPMD page');
      logger('Photo distribution: $_measurementPhotos');
    } catch (e) {
      logger('Error loading saved photos: $e');
    }
  }

  /// Load saved QuestionAnswer data from database and populate measurement widgets
  Future<void> _loadSavedQuestionAnswers() async {
    if (widget.taskId == null ||
        widget.formId == null ||
        widget.question?.questionId == null) {
      logger(
          'TaskId, FormId, or QuestionId is null, cannot load saved answers');
      return;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        logger('Task not found in database for taskId: ${widget.taskId}');
        return;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        logger('Form not found in task for formId: ${widget.formId}');
        return;
      }

      // Filter QuestionAnswers for this specific question and questionPart
      final questionAnswers = formModel.questionAnswers
          .where((qa) =>
              qa.questionId == widget.question!.questionId!.toInt() &&
              qa.questionpartId == widget.questionPart?.questionpartId?.toInt())
          .toList();

      if (questionAnswers.isEmpty) {
        logger('No saved answers found for this question');
        return;
      }

      logger(
          'Found ${questionAnswers.length} saved answers for question ${widget.question!.questionId}');

      // Populate measurement values from saved data
      for (final qa in questionAnswers) {
        if (qa.measurementId != null) {
          final measurementId = qa.measurementId!;

          // Find the measurement to determine its type
          final measurement = _findMeasurementById(measurementId);
          if (measurement == null) continue;

          // Restore value based on measurement type
          dynamic restoredValue =
              _restoreValueFromQuestionAnswer(qa, measurement);

          if (restoredValue != null) {
            setState(() {
              _measurementValues[measurementId] = restoredValue;
            });

            logger(
                'Restored value for measurement $measurementId: $restoredValue');
          }
        }
      }

      // Process conditional logic after loading all values
      _processInitialConditionalLogic();

      logger('Successfully loaded and restored saved question answers');
    } catch (e) {
      logger('Error loading saved question answers: $e');
    }
  }

  /// Restore value from QuestionAnswer based on measurement type
  dynamic _restoreValueFromQuestionAnswer(
      QuestionAnswerModel qa, Measurement measurement) {
    switch (measurement.measurementTypeId) {
      case 1: // Text field
      case 2: // Text field
        return qa.measurementTextResult ?? '';

      case 3: // Checkbox
        // For checkbox, check if measurementOptionId is set (true) or null/0 (false)
        return qa.measurementOptionId != null && qa.measurementOptionId! > 0;

      case 4: // Dropdown
      case 5: // Dropdown
        // Find the option description by measurementOptionId
        if (qa.measurementOptionId != null &&
            measurement.measurementOptions != null) {
          final option = measurement.measurementOptions!
              .where((opt) => opt.measurementOptionId == qa.measurementOptionId)
              .firstOrNull;
          return option?.measurementOptionDescription;
        }
        return null;

      case 6: // Multi-select
        // Parse measurementOptionIds string back to list of IDs (not descriptions)
        if (qa.measurementOptionIds != null &&
            qa.measurementOptionIds!.isNotEmpty) {
          try {
            final optionIds = qa.measurementOptionIds!
                .split(',')
                .map((id) => id.trim())
                .where((id) => id.isNotEmpty)
                .toList();

            logger('Restored multi-select option IDs: $optionIds');
            return optionIds;
          } catch (e) {
            logger('Error parsing multi-select options: $e');
            return <String>[];
          }
        }
        return <String>[];

      case 7: // Counter
        // Parse text result as integer
        if (qa.measurementTextResult != null) {
          return int.tryParse(qa.measurementTextResult!) ?? 0;
        }
        return 0;

      case 9: // Date picker
        // Parse text result as DateTime
        if (qa.measurementTextResult != null) {
          try {
            String dateString = qa.measurementTextResult!;
            return dateString;
          } catch (e) {
            logger('Error parsing date: $e');
            return null;
          }
        }
        return null;

      default:
        return qa.measurementTextResult;
    }
  }

  void _updateMeasurementValue(num measurementId, dynamic value) {
    setState(() {
      _measurementValues[measurementId] = value;
      // Clear validation error when value changes
      _validationErrors[measurementId] = null;

      // Check for conditional logic when widget values change
      final measurement = _findMeasurementById(measurementId);
      if (measurement != null && _canTriggerConditionalLogic(measurement)) {
        // Reset all conditional dependencies before processing new logic
        _resetConditionalDependencies(measurement);
        _processConditionalLogic(measurement, value);
      }
    });

    // Auto-save the field if it's valid
    _autoSaveField(measurementId);
  }

  /// Auto-save a single field if it passes validation
  /// This method performs silent validation and saves without showing errors
  Future<void> _autoSaveField(num measurementId) async {
    final measurement = _findMeasurementById(measurementId);
    if (measurement == null) return;

    final value = _measurementValues[measurementId];

    // Skip auto-save if value is empty
    if (_isValueEmpty(value, measurement.measurementTypeId)) return;

    // Perform silent validation (no error display)
    final validationError = _validateMeasurement(measurement, value);
    final photoError = _validatePhotos(measurement);
    final quizError =
        widget.formId != null ? _validateQuizAnswers(measurement, value) : null;

    // Only auto-save if all validations pass
    if (validationError == null && photoError == null && quizError == null) {
      // Create a single QuestionAnswer for this measurement
      final questionAnswer = _generateSingleQuestionAnswer(measurement, value);
      if (questionAnswer != null) {
        await _saveQuestionAnswersToDatabase([questionAnswer]);
      }
    }
  }

  /// Generate a single QuestionAnswer for a specific measurement
  QuestionAnswer? _generateSingleQuestionAnswer(
      Measurement measurement, dynamic value,
      {bool forceIncludeHidden = false}) {
    if (measurement.measurementId == null) return null;

    final measurementId = measurement.measurementId!;

    // Only include answers for visible widgets, unless forceIncludeHidden is true
    final isVisible = _widgetVisibility[measurementId] ?? true;
    if (!isVisible && !forceIncludeHidden) return null;

    // Create QuestionAnswer object
    final questionAnswer = QuestionAnswer(
      taskId: widget.taskId,
      flip: widget.question?.flip,
      formId: widget.formId,
      questionId: widget.question?.questionId,
      isComment: false,
      commentTypeId: null,
      questionpartId: widget.questionPart?.questionpartId,
      questionPartMultiId: widget.questionPart?.questionpartId?.toString(),
      measurementId: measurementId,
      measurementTypeId: measurement.measurementTypeId,
    );

    // Set values based on measurement type
    if ([1, 2, 9, 7].contains(measurement.measurementTypeId)) {
      // Case 1: Text Field, Date Picker, Counter
      questionAnswer.measurementOptionId = null;
      questionAnswer.measurementOptionIds = null;

      if (measurement.measurementTypeId == 3) {
        // Checkbox case
        questionAnswer.measurementTextResult = value == true ? '1' : '2';
      } else {
        // Text field, date picker, counter
        questionAnswer.measurementTextResult =
            _isValueEmpty(value, measurement.measurementTypeId)
                ? null
                : value.toString();
      }
    } else if ([4, 5].contains(measurement.measurementTypeId)) {
      // Case 2: Dropdown
      if (value != null && measurement.measurementOptions != null) {
        // Find the selected option
        final selectedOption = measurement.measurementOptions!.firstWhere(
            (option) => option.measurementOptionDescription == value,
            orElse: () => MeasurementOption());

        questionAnswer.measurementOptionId = selectedOption.measurementOptionId;
        questionAnswer.measurementOptionIds = null;
        questionAnswer.measurementTextResult = value.toString();
      } else {
        questionAnswer.measurementOptionId = null;
        questionAnswer.measurementOptionIds = null;
        questionAnswer.measurementTextResult = null;
      }
    } else if (measurement.measurementTypeId == 3) {
      // Case 3: Checkbox
      questionAnswer.measurementOptionId = value == true ? 1 : 2;
      questionAnswer.measurementOptionIds = null;
      questionAnswer.measurementTextResult = value == true ? '1' : '2';
    } else if (measurement.measurementTypeId == 6) {
      // Case 4: Multi-select
      if (value != null &&
          value is List<String> &&
          measurement.measurementOptions != null) {
        // Value already contains option IDs as strings
        final optionIds = value
            .map((id) => int.tryParse(id))
            .where((id) => id != null)
            .cast<int>()
            .toList();

        // Get descriptions for logging and text result
        final selectedDescriptions = <String>[];
        for (final optionId in optionIds) {
          final option = measurement.measurementOptions!.firstWhere(
              (opt) => opt.measurementOptionId == optionId,
              orElse: () => MeasurementOption());
          if (option.measurementOptionDescription != null) {
            selectedDescriptions.add(option.measurementOptionDescription!);
          }
        }

        questionAnswer.measurementOptionId = null;
        questionAnswer.measurementOptionIds = optionIds.join(',');
        questionAnswer.measurementTextResult = selectedDescriptions.join('|');
      } else {
        questionAnswer.measurementOptionId = null;
        questionAnswer.measurementOptionIds = null;
        questionAnswer.measurementTextResult = null;
      }
    }

    return questionAnswer;
  }

  /// Find a measurement by its ID
  Measurement? _findMeasurementById(num measurementId) {
    final measurements = widget.question?.measurements ?? [];
    try {
      return measurements.firstWhere(
        (measurement) => measurement.measurementId == measurementId,
      );
    } catch (e) {
      return null;
    }
  }

  /// Check if a measurement can trigger conditional logic
  bool _canTriggerConditionalLogic(Measurement measurement) {
    // Check if this measurement has any conditional logic defined
    final hasConditions =
        (measurement.measurementConditions?.isNotEmpty ?? false) ||
            (measurement.measurementConditionsMultiple?.isNotEmpty ?? false);

    // Only certain widget types can trigger conditional logic
    final canTrigger = [3, 4, 5, 6, 8].contains(measurement.measurementTypeId);

    return hasConditions && canTrigger;
  }

  /// Process conditional logic for widget selections
  ///
  /// This method implements the conditional logic based on the question's is_mll property:
  /// - If is_mll is false: Uses measurement_conditions array
  /// - If is_mll is true: Uses measurement_conditions_multiple array
  ///
  /// For each condition, it checks if:
  /// - measurementId equals the current measurement ID
  /// - measurementOptionId equals the selected option ID (for dropdowns/radio/multi-select)
  /// - For checkboxes, it uses predefined option IDs (1 for true, 2 for false)
  ///
  /// If both conditions are satisfied, it applies the action (appear/disappear)
  /// to the target widget specified by action_measurement_id
  void _processConditionalLogic(
      Measurement measurement, dynamic selectedValue) {
    if (widget.question?.isMll == null || selectedValue == null) return;

    // Get the selected option ID based on widget type
    final selectedOptionId =
        _getSelectedOptionIdForConditionalLogic(measurement, selectedValue);
    if (selectedOptionId == null) return;

    // Check conditions based on is_mll property
    if (widget.question!.isMll == false) {
      // Use measurement_conditions array
      _processConditions(
        measurement.measurementConditions,
        measurement.measurementId,
        selectedOptionId,
      );
    } else {
      // Use measurement_conditions_multiple array
      _processConditions(
        measurement.measurementConditionsMultiple,
        measurement.measurementId,
        selectedOptionId,
      );
    }
  }

  /// Get the measurement option ID for conditional logic based on widget type
  num? _getSelectedOptionIdForConditionalLogic(
      Measurement measurement, dynamic selectedValue) {
    switch (measurement.measurementTypeId) {
      case 3: // Checkbox
        // For checkboxes, use 1 for true and 2 for false
        return selectedValue == true ? 1 : 2;

      case 4: // Dropdown
      case 5: // Dropdown
      case 8: // Radio button
        return _getSelectedOptionId(measurement, selectedValue);

      case 6: // Multi-select
        // For multi-select, we need to handle multiple selected options
        // This is more complex and might need special handling
        // For now, return null as multi-select conditional logic might need different approach
        return null;

      default:
        return null;
    }
  }

  /// Get the measurement option ID for the selected value
  num? _getSelectedOptionId(Measurement measurement, dynamic selectedValue) {
    if (measurement.measurementOptions == null) return null;

    try {
      final selectedOption = measurement.measurementOptions!.firstWhere(
        (option) => option.measurementOptionDescription == selectedValue,
      );
      return selectedOption.measurementOptionId;
    } catch (e) {
      return null;
    }
  }

  /// Process measurement conditions array
  void _processConditions(
    List<MeasurementCondition>? conditions,
    num? currentMeasurementId,
    num selectedOptionId,
  ) {
    if (conditions == null || currentMeasurementId == null) return;

    for (final condition in conditions) {
      // Check if this condition applies to the current measurement and selected option
      if (condition.measurementId == currentMeasurementId &&
          condition.measurementOptionId == selectedOptionId) {
        // Apply the action to the target measurement
        if (condition.actionMeasurementId != null && condition.action != null) {
          _applyConditionalAction(
            condition.actionMeasurementId!,
            condition.action!,
          );
        }
      }
    }
  }

  /// Reset all conditional dependencies for a measurement that's about to change
  /// This ensures that when a dropdown value changes, all previously shown dependent widgets
  /// are properly hidden and their values cleared before applying new conditional logic
  void _resetConditionalDependencies(Measurement measurement) {
    if (measurement.measurementId == null) return;

    // Get all target measurement IDs that this measurement can affect
    final targetMeasurementIds = <num>{};

    // Check measurement_conditions array
    if (widget.question?.isMll == false &&
        measurement.measurementConditions != null) {
      for (final condition in measurement.measurementConditions!) {
        if (condition.actionMeasurementId != null) {
          targetMeasurementIds.add(condition.actionMeasurementId!);
        }
      }
    }

    // Check measurement_conditions_multiple array
    if (widget.question?.isMll == true &&
        measurement.measurementConditionsMultiple != null) {
      for (final condition in measurement.measurementConditionsMultiple!) {
        if (condition.actionMeasurementId != null) {
          targetMeasurementIds.add(condition.actionMeasurementId!);
        }
      }
    }

    // Hide all target widgets and clear their values
    for (final targetId in targetMeasurementIds) {
      _hideWidgetAndCascade(targetId);
    }
  }

  /// Hide a widget and cascade the hiding to all its dependent widgets
  void _hideWidgetAndCascade(num measurementId) {
    // Hide the widget
    _widgetVisibility[measurementId] = false;

    // Clear its value and validation errors using proper type-specific clearing
    _clearWidgetValue(measurementId);
    _validationErrors[measurementId] = null;

    // Find all widgets that depend on this widget and hide them recursively
    final measurements = widget.question?.measurements ?? [];
    final dependentMeasurementIds = <num>{};

    for (final measurement in measurements) {
      if (measurement.measurementId == measurementId) continue;

      // Check measurement_conditions array
      if (widget.question?.isMll == false &&
          measurement.measurementConditions != null) {
        for (final condition in measurement.measurementConditions!) {
          if (condition.measurementId == measurementId &&
              condition.actionMeasurementId != null) {
            dependentMeasurementIds.add(condition.actionMeasurementId!);
          }
        }
      }

      // Check measurement_conditions_multiple array
      if (widget.question?.isMll == true &&
          measurement.measurementConditionsMultiple != null) {
        for (final condition in measurement.measurementConditionsMultiple!) {
          if (condition.measurementId == measurementId &&
              condition.actionMeasurementId != null) {
            dependentMeasurementIds.add(condition.actionMeasurementId!);
          }
        }
      }
    }

    // Recursively hide dependent widgets
    for (final dependentId in dependentMeasurementIds) {
      if (_widgetVisibility[dependentId] == true) {
        _hideWidgetAndCascade(dependentId);
      }
    }
  }

  /// Clear widget value based on its measurement type
  void _clearWidgetValue(num measurementId) {
    final measurement = _findMeasurementById(measurementId);
    if (measurement == null) {
      _measurementValues[measurementId] = null;
      return;
    }

    switch (measurement.measurementTypeId) {
      case 1: // Text field
      case 2: // Text field
        _measurementValues[measurementId] = '';
        break;
      case 3: // Checkbox
        _measurementValues[measurementId] = false;
        break;
      case 4: // Dropdown
      case 5: // Dropdown
      case 8: // Radio button
      case 9: // Date picker
        _measurementValues[measurementId] = null;
        break;
      case 6: // Multi-select
        _measurementValues[measurementId] = <String>[];
        break;
      case 7: // Counter
        _measurementValues[measurementId] = 0;
        break;
      default:
        _measurementValues[measurementId] = null;
    }
  }

  /// Apply conditional action (show/hide) to a target measurement widget
  void _applyConditionalAction(num targetMeasurementId, String action) {
    final previousVisibility = _widgetVisibility[targetMeasurementId] ?? true;

    // Update widget visibility based on action
    if (action.toLowerCase() == 'appear') {
      _widgetVisibility[targetMeasurementId] = true;
    } else if (action.toLowerCase() == 'disappear') {
      _widgetVisibility[targetMeasurementId] = false;
      // Clear validation error when widget becomes hidden
      _validationErrors[targetMeasurementId] = null;
      // Clear the widget's value when it becomes hidden using proper type-specific clearing
      _clearWidgetValue(targetMeasurementId);

      // Cascade hiding to dependent widgets
      _hideWidgetAndCascade(targetMeasurementId);
    }

    // If visibility changed, clear any existing validation errors for the target widget
    final newVisibility = _widgetVisibility[targetMeasurementId] ?? true;
    if (previousVisibility != newVisibility) {
      _validationErrors[targetMeasurementId] = null;
    }
  }

  /// Validate a single measurement value
  String? _validateMeasurement(Measurement measurement, dynamic value) {
    if (measurement.measurementValidations == null) return null;

    for (final validation in measurement.measurementValidations!) {
      // Validation Type 1: Required validation
      if (validation.validationTypeId == 1 && validation.required == true) {
        if (_isValueEmpty(value, measurement.measurementTypeId)) {
          return validation.errorMessage ?? 'This field is required';
        }
      }

      // Validation Type 2: Range validation
      if (validation.validationTypeId == 2 &&
          validation.rangeValidation != null &&
          validation.rangeValidation!.isNotEmpty) {
        logger('Range validation: $value');
        value = int.tryParse(value.toString());
        if (!_isValueEmpty(value, measurement.measurementTypeId)) {
          if (!_validateRange(value, validation.rangeValidation!)) {
            return validation.errorMessage ??
                'Value is not within the valid range';
          }
        }
      }

      // Validation Type 3: Expression validation (regex)
      if (validation.validationTypeId == 3 &&
          validation.expressionValidation != null &&
          validation.expressionValidation!.isNotEmpty) {
        if (!_isValueEmpty(value, measurement.measurementTypeId)) {
          if (!_validateExpression(value, validation.expressionValidation!)) {
            return validation.errorMessage ??
                'Value does not match the required format';
          }
        }
      }
    }

    return null;
  }

  /// Validate photo requirements for a measurement
  String? _validatePhotos(Measurement measurement) {
    if (measurement.measurementId == null) return null;

    // Check if this measurement has photo requirements
    final cameraInfo = _getCameraIconInfo(measurement);
    if (!cameraInfo['show']) return null; // No photo requirement

    // Find the corresponding PhotoTagsThree object
    final photoTag = _getPhotoTagForMeasurement(measurement);
    if (photoTag == null) return null;

    // Get the required number of photos
    final requiredPhotos = photoTag.numberOfPhotos?.toInt() ?? 0;
    if (requiredPhotos <= 0) return null; // No specific requirement

    // Get the current number of uploaded photos
    final measurementId = measurement.measurementId!;
    final uploadedPhotos = _measurementPhotos[measurementId]?.length ?? 0;

    logger(
        'Photo validation for measurement $measurementId: required=$requiredPhotos, uploaded=$uploadedPhotos');

    // Check if photo requirement is met
    if (uploadedPhotos < requiredPhotos) {
      final isMandatory = cameraInfo['isMandatory'] as bool;
      if (isMandatory) {
        return 'Please upload at least $requiredPhotos photo(s). Currently uploaded: $uploadedPhotos';
      } else {
        // For optional photos, we might want to show a warning but not block submission
        // For now, we'll treat it as a validation error if numberOfPhotos is specified
        return 'Please upload at least $requiredPhotos photo(s). Currently uploaded: $uploadedPhotos';
      }
    }

    return null;
  }

  /// Get the PhotoTagsThree object for a specific measurement
  PhotoTagsT? _getPhotoTagForMeasurement(Measurement measurement) {
    if (widget.question?.photoTagsThree == null ||
        measurement.measurementId == null) {
      return null;
    }

    // Find the photo tag that matches this measurement
    for (final photoTag in widget.question!.photoTagsThree!) {
      if (photoTag.measurementId == measurement.measurementId &&
          photoTag.questionpartId == widget.questionPart?.questionpartId) {
        return photoTag;
      }
    }

    return null;
  }

  /// Check if a value is considered empty based on measurement type
  bool _isValueEmpty(dynamic value, num? measurementTypeId) {
    switch (measurementTypeId) {
      case 1: // Text field
      case 2: // Text field
        return value == null || value.toString().trim().isEmpty;
      case 3: // Checkbox
        return value == null || value == false;
      case 4: // Dropdown
      case 5: // Dropdown
      case 9: // Date picker
        return value == null;
      case 6: // Multi-select
        return value == null || (value is List && value.isEmpty);
      case 7: // Counter
        return value == null || value == 0;
      default:
        return value == null;
    }
  }

  /// Validate range for numeric values
  bool _validateRange(dynamic value, String rangeValidation) {
    try {
      final ranges = rangeValidation.split('|');
      final numValue = double.tryParse(value.toString());
      if (numValue == null) return false;
      final min = double.tryParse(ranges[0].trim());
      final max = double.tryParse(ranges[1].trim());
      if (min != null && max != null) {
        if (numValue >= min && numValue <= max) {
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Validate expression using regex
  bool _validateExpression(dynamic value, String expressionValidation) {
    try {
      final regex = RegExp(expressionValidation);
      return regex.hasMatch(value.toString());
    } catch (e) {
      return false;
    }
  }

  /// Check if a measurement is required based on measurement_validations array
  bool _isMeasurementRequired(Measurement measurement) {
    if (measurement.measurementValidations == null) return false;

    for (final validation in measurement.measurementValidations!) {
      if (validation.required == true) {
        return true;
      }
    }
    return false;
  }

  /// Validate quiz answers for form_type_id = 12
  String? _validateQuizAnswers(Measurement measurement, dynamic value) {
    // Only validate for quiz forms (form_type_id = 12)
    if (!_isQuizForm()) return null;

    // Only validate dropdown and multi-select widgets
    if (![4, 5, 6].contains(measurement.measurementTypeId)) return null;

    // Skip validation if value is empty (handled by required validation)
    if (_isValueEmpty(value, measurement.measurementTypeId)) return null;

    if ([4, 5].contains(measurement.measurementTypeId)) {
      // Dropdown validation: Check if selected answer is correct
      return _validateDropdownQuizAnswer(measurement, value);
    } else if (measurement.measurementTypeId == 6) {
      // Multi-select validation: Check if all correct answers are selected
      return _validateMultiSelectQuizAnswer(measurement, value);
    }

    return null;
  }

  /// Check if current form is a quiz form (form_type_id = 12)
  bool _isQuizForm() {
    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) return false;

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      return formModel?.formTypeId == 12;
    } catch (e) {
      logger('Error checking form type: $e');
      return false;
    }
  }

  /// Validate dropdown quiz answer (types 4 and 5)
  String? _validateDropdownQuizAnswer(Measurement measurement, dynamic value) {
    if (measurement.measurementOptions == null) return null;

    try {
      // Find the selected option
      final selectedOption = measurement.measurementOptions!.firstWhere(
        (option) => option.measurementOptionDescription == value,
      );

      // Check if the selected answer is correct
      if (selectedOption.isAnswer != true) {
        return 'The answer you selected is incorrect';
      }
    } catch (e) {
      // Option not found
      return 'The answer you selected is incorrect';
    }

    return null;
  }

  /// Validate multi-select quiz answer (type 6)
  String? _validateMultiSelectQuizAnswer(
      Measurement measurement, dynamic value) {
    if (measurement.measurementOptions == null) return null;
    if (value is! List) return null;

    // Get all correct answers (where is_answer is true)
    final correctOptions = measurement.measurementOptions!
        .where((option) => option.isAnswer == true)
        .toList();

    // Get all incorrect answers (where is_answer is false)
    final incorrectOptions = measurement.measurementOptions!
        .where((option) => option.isAnswer == false)
        .toList();

    if (correctOptions.isEmpty) return null;

    // Convert selected values (option IDs as strings) to integers for comparison
    final selectedOptionIds = <int>[];
    for (final selectedValue in value) {
      final optionId = int.tryParse(selectedValue.toString());
      if (optionId != null) {
        selectedOptionIds.add(optionId);
      }
    }

    // Check if all correct answers are selected
    for (final correctOption in correctOptions) {
      if (!selectedOptionIds
          .contains(correctOption.measurementOptionId?.toInt())) {
        return 'Some of the answers are incorrect';
      }
    }

    // Check if any incorrect answers are selected
    for (final incorrectOption in incorrectOptions) {
      if (selectedOptionIds
          .contains(incorrectOption.measurementOptionId?.toInt())) {
        return 'Some of the answers are incorrect';
      }
    }

    return null;
  }

  /// Generate QuestionAnswer array from current measurement values
  List<QuestionAnswer> _generateQuestionAnswers({bool includeHiddenWithValues = false}) {
    final questionAnswers = <QuestionAnswer>[];
    final measurements = widget.question?.measurements ?? [];

    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      final measurementId = measurement.measurementId!;
      final value = _measurementValues[measurementId];

      // Check if widget should be included
      final isVisible = _widgetVisibility[measurementId] ?? true;
      final hasValue = !_isValueEmpty(value, measurement.measurementTypeId);

      // Include if visible, or if hidden but has value and includeHiddenWithValues is true
      if (!isVisible && !(includeHiddenWithValues && hasValue)) continue;

      // Create QuestionAnswer object
      final questionAnswer = QuestionAnswer(
        taskId: widget.taskId,
        flip: widget.question?.flip,
        formId: widget.formId,
        questionId: widget.question?.questionId,
        isComment: false,
        commentTypeId: null,
        questionpartId: widget.questionPart?.questionpartId,
        questionPartMultiId: widget.questionPart?.questionpartId?.toString(),
        measurementId: measurementId,
        measurementTypeId: measurement.measurementTypeId,
      );

      // Set values based on measurement type
      if ([1, 2, 9, 7].contains(measurement.measurementTypeId)) {
        // Case 1: Text Field, Date Picker, Counter
        questionAnswer.measurementOptionId = null;
        questionAnswer.measurementOptionIds = null;

        if (measurement.measurementTypeId == 3) {
          // Checkbox case
          questionAnswer.measurementTextResult = value == true ? '1' : '2';
        } else {
          // Text field, date picker, counter
          questionAnswer.measurementTextResult =
              _isValueEmpty(value, measurement.measurementTypeId)
                  ? null
                  : value.toString();
        }
      } else if ([4, 5].contains(measurement.measurementTypeId)) {
        // Case 2: Dropdown
        if (value != null && measurement.measurementOptions != null) {
          // Find the selected option
          final selectedOption = measurement.measurementOptions!.firstWhere(
              (option) => option.measurementOptionDescription == value,
              orElse: () => MeasurementOption());

          questionAnswer.measurementOptionId =
              selectedOption.measurementOptionId;
          questionAnswer.measurementOptionIds = null;
          questionAnswer.measurementTextResult = value.toString();
        } else {
          questionAnswer.measurementOptionId = null;
          questionAnswer.measurementOptionIds = null;
          questionAnswer.measurementTextResult = null;
        }
      } else if (measurement.measurementTypeId == 3) {
        // Case 3: Checkbox
        questionAnswer.measurementOptionId = value == true ? 1 : 2;
        questionAnswer.measurementOptionIds = null;
        questionAnswer.measurementTextResult = value == true ? '1' : '2';
      } else if (measurement.measurementTypeId == 6) {
        // Case 4: Multi-select
        if (value != null &&
            value is List<String> &&
            measurement.measurementOptions != null) {
          // Value already contains option IDs as strings
          final optionIds = value
              .map((id) => int.tryParse(id))
              .where((id) => id != null)
              .cast<int>()
              .toList();

          // Get descriptions for logging and text result
          final selectedDescriptions = <String>[];
          for (final optionId in optionIds) {
            final option = measurement.measurementOptions!.firstWhere(
                (opt) => opt.measurementOptionId == optionId,
                orElse: () => MeasurementOption());
            if (option.measurementOptionDescription != null) {
              selectedDescriptions.add(option.measurementOptionDescription!);
            }
          }

          questionAnswer.measurementOptionId = null;
          questionAnswer.measurementOptionIds = optionIds.join(',');
          questionAnswer.measurementTextResult = selectedDescriptions.join('|');
          logger(
              'Generated multi-select answer - IDs: $optionIds, Descriptions: $selectedDescriptions');
        } else {
          questionAnswer.measurementOptionId = null;
          questionAnswer.measurementOptionIds = null;
          questionAnswer.measurementTextResult = null;
        }
      }

      questionAnswers.add(questionAnswer);
    }

    return questionAnswers;
  }

  /// Save QuestionAnswer data to the database
  Future<bool> _saveQuestionAnswersToDatabase(
      List<QuestionAnswer> questionAnswers) async {
    if (widget.taskId == null || widget.formId == null) {
      logger('TaskId or FormId is null, cannot save answers');
      return false;
    }

    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [widget.taskId!.toInt()]).firstOrNull;

      if (taskModel == null) {
        logger('Task not found in database for taskId: ${widget.taskId}');
        return false;
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == widget.formId!.toInt())
          .firstOrNull;

      if (formModel == null) {
        logger('Form not found in task for formId: ${widget.formId}');
        return false;
      }

      // Convert QuestionAnswer entities to models
      final questionAnswerModels = questionAnswers
          .map((qa) => QuestionAnswerModel(
                taskId: qa.taskId?.toInt(),
                formId: qa.formId?.toInt(),
                questionId: qa.questionId?.toInt(),
                questionpartId: qa.questionpartId?.toInt(),
                flip: qa.flip,
                questionPartMultiId: qa.questionPartMultiId,
                measurementId: qa.measurementId?.toInt(),
                measurementTypeId: qa.measurementTypeId?.toInt(),
                measurementOptionId: qa.measurementOptionId?.toInt(),
                measurementOptionIds: qa.measurementOptionIds,
                measurementTextResult: qa.measurementTextResult,
                isComment: qa.isComment,
                commentTypeId: qa.commentTypeId?.toInt(),
              ))
          .toList();

      // Save to database in a write transaction
      realm.write(() {
        // Remove existing answers for this question and questionPart
        final existingAnswers = formModel.questionAnswers
            .where((qa) =>
                qa.questionId == widget.question!.questionId!.toInt() &&
                qa.questionpartId ==
                    widget.questionPart?.questionpartId?.toInt())
            .toList();

        for (final existingAnswer in existingAnswers) {
          formModel.questionAnswers.remove(existingAnswer);
        }

        // Add new answers
        for (final newAnswer in questionAnswerModels) {
          formModel.questionAnswers.add(newAnswer);
        }
      });

      logger(
          'Successfully saved ${questionAnswerModels.length} question answers to database');
      return true;
    } catch (e) {
      logger('Error saving question answers to database: $e');
      return false;
    }
  }

  /// Validate all measurements and return true if all are valid
  /// Uses path-based validation to only validate currently visible widgets
  bool _validateAllMeasurements() {
    bool isValid = true;
    final measurements = widget.question?.measurements ?? [];

    setState(() {
      _validationErrors.clear();
    });

    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      final measurementId = measurement.measurementId!;

      // Check if widget should be validated (only visible widgets)
      if (!_shouldValidateMeasurement(measurement)) continue;

      final value = _measurementValues[measurementId];

      // Perform standard validation
      final error = _validateMeasurement(measurement, value);
      if (error != null) {
        setState(() {
          _validationErrors[measurementId] = error;
        });
        isValid = false;
      }

      // Perform photo validation if no other errors
      if (error == null) {
        final photoError = _validatePhotos(measurement);
        if (photoError != null) {
          setState(() {
            _validationErrors[measurementId] = photoError;
          });
          isValid = false;
        }

        // Perform quiz validation if no standard or photo errors (for form_type_id = 12)
        if (photoError == null && widget.formId != null) {
          final quizError = _validateQuizAnswers(measurement, value);
          if (quizError != null) {
            setState(() {
              _validationErrors[measurementId] = quizError;
            });
            isValid = false;
          }
        }
      }
    }

    // Validate that the current path through conditional logic is complete
    if (isValid) {
      isValid = _validateConditionalPaths();
    }

    return isValid;
  }

  /// Determine if a measurement should be validated
  /// Only validate currently visible widgets to support path-based validation
  bool _shouldValidateMeasurement(Measurement measurement) {
    if (measurement.measurementId == null) return false;

    final measurementId = measurement.measurementId!;
    final isCurrentlyVisible = _widgetVisibility[measurementId] ?? true;

    // Only validate currently visible widgets
    // This ensures we only validate widgets that are part of the user's current path
    // through the conditional logic, not all possible widgets that could become visible
    return isCurrentlyVisible;
  }

  /// Validate that at least one complete path through the conditional logic is valid
  /// This ensures that the user has completed a valid flow through the conditional widgets
  bool _validateConditionalPaths() {
    final measurements = widget.question?.measurements ?? [];

    // Get all root measurements (those that are not targets of conditional actions)
    final rootMeasurements = <Measurement>[];
    final targetMeasurementIds = <num>{};

    // First, identify all measurements that are targets of conditional actions
    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      // Check measurement_conditions array
      if (widget.question?.isMll == false &&
          measurement.measurementConditions != null) {
        for (final condition in measurement.measurementConditions!) {
          if (condition.actionMeasurementId != null) {
            targetMeasurementIds.add(condition.actionMeasurementId!);
          }
        }
      }

      // Check measurement_conditions_multiple array
      if (widget.question?.isMll == true &&
          measurement.measurementConditionsMultiple != null) {
        for (final condition in measurement.measurementConditionsMultiple!) {
          if (condition.actionMeasurementId != null) {
            targetMeasurementIds.add(condition.actionMeasurementId!);
          }
        }
      }
    }

    // Root measurements are those not targeted by any conditional action
    for (final measurement in measurements) {
      if (measurement.measurementId != null &&
          !targetMeasurementIds.contains(measurement.measurementId!)) {
        rootMeasurements.add(measurement);
      }
    }

    // If no conditional logic exists, all visible widgets should be valid
    if (rootMeasurements.length == measurements.length) {
      return true; // No conditional logic, standard validation applies
    }

    // For conditional logic, ensure all visible widgets in the current path are valid
    // This is already handled by the standard validation since we only validate visible widgets
    return true;
  }

  /// Handle save button press
  void _handleSave() async {
    if (_validateAllMeasurements()) {
      final questionAnswers = _generateQuestionAnswers();
      logger('Generated ${questionAnswers.length} question answers for saving');

      // Save questionAnswers to database
      final saveSuccess = await _saveQuestionAnswersToDatabase(questionAnswers);

      if (mounted) {
        if (saveSuccess) {
          SnackBarService.success(
            context: context,
            message: 'Answers saved successfully!',
          );
          // Navigate back
          Navigator.of(context).pop();
        } else {
          SnackBarService.error(
            context: context,
            message: 'Failed to save answers. Please try again.',
          );
        }
      }
    } else {
      // Show error message for visible validation errors only
      SnackBarService.error(
        context: context,
        message: 'Please fix the validation errors before saving.',
      );
    }
  }

  /// Calculate the number of completed measurements
  /// A measurement is considered completed if it has a valid value and passes validation
  int _getCompletedMeasurementsCount() {
    final measurements = widget.question?.measurements ?? [];
    int completedCount = 0;

    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      final measurementId = measurement.measurementId!;

      // Only count visible measurements
      if (!(_widgetVisibility[measurementId] ?? true)) continue;

      final value = _measurementValues[measurementId];

      // Check if measurement has a value and passes validation
      if (!_isValueEmpty(value, measurement.measurementTypeId)) {
        // Check if it passes all validations
        final error = _validateMeasurement(measurement, value);
        final photoError = _validatePhotos(measurement);
        final quizError = widget.formId != null
            ? _validateQuizAnswers(measurement, value)
            : null;

        if (error == null && photoError == null && quizError == null) {
          completedCount++;
        }
      }
    }

    return completedCount;
  }

  /// Calculate the total number of visible measurements
  int _getTotalVisibleMeasurementsCount() {
    final measurements = widget.question?.measurements ?? [];
    int totalCount = 0;

    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      final measurementId = measurement.measurementId!;

      // Only count visible measurements
      if (_widgetVisibility[measurementId] ?? true) {
        totalCount++;
      }
    }

    return totalCount;
  }

  /// Check if all visible measurements are completed
  bool _areAllMeasurementsCompleted() {
    final completedCount = _getCompletedMeasurementsCount();
    final totalCount = _getTotalVisibleMeasurementsCount();
    return totalCount > 0 && completedCount == totalCount;
  }

  /// Build the bottom progress bar widget
  Widget _buildBottomProgressBar() {
    final completedCount = _getCompletedMeasurementsCount();
    final totalCount = _getTotalVisibleMeasurementsCount();
    final isCompleted = _areAllMeasurementsCompleted();
    final progress = totalCount > 0 ? completedCount / totalCount : 0.0;

    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        color: isCompleted ? AppColors.green15 : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        child: isCompleted
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Completed',
                    style: AppTypography.montserratTitleExtraSmall,
                  ),
                  Icon(
                    Icons.check,
                    color: AppColors.black,
                    size: 20,
                  ),
                ],
              )
            : Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: LinearProgressIndicator(
                      value: progress,
                      backgroundColor: AppColors.green15,
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        AppColors.loginGreen,
                      ),
                      minHeight: 4,
                    ),
                  ),
                  const Gap(32),
                  Text(
                    '$completedCount of $totalCount',
                    style: AppTypography.montserratTableSmall,
                  ),
                ],
              ),
      ),
    );
  }

  /// Get camera icon info for a measurement based on photo_tags_three array
  Map<String, dynamic> _getCameraIconInfo(Measurement measurement) {
    final result = {'show': false, 'isMandatory': false};

    if (widget.question?.photoTagsThree == null ||
        measurement.measurementId == null) {
      return result;
    }

    // Check photo_tags_three array for matching questionpart_id and measurement_id
    for (final photoTag in widget.question!.photoTagsThree!) {
      if (photoTag.measurementId == measurement.measurementId &&
          photoTag.questionpartId == widget.questionPart?.questionpartId) {
        result['show'] = true;
        result['isMandatory'] = photoTag.isMandatory == true;
        break;
      }
    }

    return result;
  }

  /// Handle navigation to MPTPage when "Add Photos" section is tapped
  void _handleAddPhotosTap(Measurement measurement) async {
    logger('Add photos tapped for measurement: ${measurement.measurementId}');
    if (widget.question?.photoTagsThree == null ||
        measurement.measurementId == null) {
      return;
    }

    // Navigate to MPTPage with level 3 for photo_tags_three
    await context.router.push(MPTRoute(
      taskId: widget.taskId?.toString(),
      formId: widget.formId?.toString(),
      questionId: widget.question?.questionId?.toString(),
      questionPartId: widget.questionPart?.questionpartId?.toString(),
      measurementId: measurement.measurementId?.toString(),
      images: const [], // Start with empty images
      question: widget.question,
      level: 3, // Use level 3 for photo_tags_three array
    ));

    // Reload photos after returning from MPTPage
    await _loadSavedPhotos();

    // Force UI refresh to show updated photos
    if (mounted) {
      setState(() {});
    }
  }

  /// Get photos for a specific measurement
  List<String> _getPhotosForMeasurement(num measurementId) {
    return _measurementPhotos[measurementId] ?? [];
  }

  /// Get photo-specific error text for a measurement
  String? _getPhotoErrorText(num measurementId) {
    final errorText = _validationErrors[measurementId];
    if (errorText != null && errorText.toLowerCase().contains('photo')) {
      return errorText;
    }
    return null;
  }

  Widget _buildMeasurementWidget(Measurement measurement) {
    final measurementId = measurement.measurementId;
    if (measurementId == null) return const SizedBox.shrink();

    // Check widget visibility based on conditional logic
    final isVisible = _widgetVisibility[measurementId] ?? true;
    if (!isVisible) return const SizedBox.shrink();

    final isRequired = _isMeasurementRequired(measurement);
    final cameraInfo = _getCameraIconInfo(measurement);
    final photoTag = _getPhotoTagForMeasurement(measurement);

    Widget measurementWidget;
    switch (measurement.measurementTypeId) {
      case 9: // Date picker
        measurementWidget = DatePickerWidget(
          measurement: measurement,
          value: _measurementValues[measurementId],
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: isRequired,
          onCameraTap: () => _handleAddPhotosTap(measurement),
          errorText: _validationErrors[measurementId],
          selectedImages: _getPhotosForMeasurement(measurementId),
          photoErrorText: _getPhotoErrorText(measurementId),
          photoTag: photoTag,
        );
        break;
      case 1: // Text field
      case 2: // Text field
        measurementWidget = TextFieldWidget(
          measurement: measurement,
          value: _measurementValues[measurementId] ?? '',
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: isRequired,
          onCameraTap: () => _handleAddPhotosTap(measurement),
          errorText: _validationErrors[measurementId],
          selectedImages: _getPhotosForMeasurement(measurementId),
          photoErrorText: _getPhotoErrorText(measurementId),
          photoTag: photoTag,
        );
        break;
      case 7: // Counter
        measurementWidget = CounterWidget(
          measurement: measurement,
          value: _measurementValues[measurementId] ?? 0,
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: isRequired,
          onCameraTap: () => _handleAddPhotosTap(measurement),
          errorText: _validationErrors[measurementId],
          selectedImages: _getPhotosForMeasurement(measurementId),
          photoErrorText: _getPhotoErrorText(measurementId),
          photoTag: photoTag,
        );
        break;
      case 4: // Dropdown
      case 5: // Dropdown
        measurementWidget = DropdownWidget(
          measurement: measurement,
          value: _measurementValues[measurementId],
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: isRequired,
          onCameraTap: () => _handleAddPhotosTap(measurement),
          errorText: _validationErrors[measurementId],
          selectedImages: _getPhotosForMeasurement(measurementId),
          photoErrorText: _getPhotoErrorText(measurementId),
          photoTag: photoTag,
        );
        break;
      case 3: // Checkbox
        measurementWidget = CheckboxWidget(
          measurement: measurement,
          value: _measurementValues[measurementId] ?? false,
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: isRequired,
          onCameraTap: () => _handleAddPhotosTap(measurement),
          errorText: _validationErrors[measurementId],
          selectedImages: _getPhotosForMeasurement(measurementId),
          photoErrorText: _getPhotoErrorText(measurementId),
          photoTag: photoTag,
        );
        break;
      case 6: // Multi-select
        measurementWidget = MultiSelectWidget(
          measurement: measurement,
          value: _measurementValues[measurementId] ?? <String>[],
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: isRequired,
          onCameraTap: () => _handleAddPhotosTap(measurement),
          errorText: _validationErrors[measurementId],
          selectedImages: _getPhotosForMeasurement(measurementId),
          photoErrorText: _getPhotoErrorText(measurementId),
          photoTag: photoTag,
        );
        break;
      case 8: // Radio button (single choice)
        measurementWidget = RadioButtonWidget(
          measurement: measurement,
          value: _measurementValues[measurementId],
          onChanged: (value) => _updateMeasurementValue(measurementId, value),
          showCameraIcon: cameraInfo['show'] as bool,
          isCameraMandatory: cameraInfo['isMandatory'] as bool,
          isRequired: isRequired,
          onCameraTap: () => _handleAddPhotosTap(measurement),
          selectedImages: _getPhotosForMeasurement(measurementId),
          photoErrorText: _getPhotoErrorText(measurementId),
          photoTag: photoTag,
        );
        break;
      default:
        measurementWidget = Card(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.0),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Text(
              'Unsupported measurement type: ${measurement.measurementTypeId}',
              style: AppTypography.montserratTableSmall.copyWith(
                color: AppColors.blackTint1,
              ),
            ),
          ),
        );
    }

    // Return the measurement widget directly since indicators are now handled within each widget
    return measurementWidget;
  }

  /// Auto-save all valid data when navigating back
  /// This method performs silent validation and saves only valid fields
  Future<void> _autoSaveOnNavigation() async {
    final measurements = widget.question?.measurements ?? [];
    final validQuestionAnswers = <QuestionAnswer>[];

    for (final measurement in measurements) {
      if (measurement.measurementId == null) continue;

      final measurementId = measurement.measurementId!;

      // Only process visible measurements
      final isVisible = _widgetVisibility[measurementId] ?? true;
      if (!isVisible) continue;

      final value = _measurementValues[measurementId];

      // Skip empty values
      if (_isValueEmpty(value, measurement.measurementTypeId)) continue;

      // Perform silent validation (no error display)
      final validationError = _validateMeasurement(measurement, value);
      final photoError = _validatePhotos(measurement);
      final quizError = widget.formId != null
          ? _validateQuizAnswers(measurement, value)
          : null;

      // Only include if all validations pass
      if (validationError == null && photoError == null && quizError == null) {
        final questionAnswer =
            _generateSingleQuestionAnswer(measurement, value);
        if (questionAnswer != null) {
          validQuestionAnswers.add(questionAnswer);
        }
      }
    }

    // Save all valid answers if any exist
    if (validQuestionAnswers.isNotEmpty) {
      await _saveQuestionAnswersToDatabase(validQuestionAnswers);
      logger(
          'Auto-saved ${validQuestionAnswers.length} valid answers on navigation');
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final measurements = widget.question?.measurements ?? [];

    return PopScope(
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          // Auto-save valid data after navigation is confirmed
          await _autoSaveOnNavigation();
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.lightGrey2,
        appBar: CustomAppBar(
          title: widget.question?.questionDescription ?? 'QPMD Page',
          actions: [
            IconButton(
              icon: const Icon(
                Icons.save_rounded,
                color: AppColors.primaryBlue,
              ),
              onPressed: _handleSave,
            ),
          ],
        ),
        body: Column(
          children: [
            Expanded(
              child: measurements.isEmpty
                  ? Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          'No measurements available for this question',
                          style: textTheme.bodyLarge?.copyWith(
                            color: AppColors.blackTint1,
                          ),
                        ),
                      ),
                    )
                  : SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.only(
                            bottom:
                                16), // Reduced padding since we have bottom bar
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Gap(12),
                            ListView.separated(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              itemCount: measurements.length,
                              itemBuilder: (context, index) {
                                final measurement = measurements[index];
                                return _buildMeasurementWidget(measurement);
                              },
                              separatorBuilder: (_, __) => const Gap(8),
                            ),
                            const Gap(24),
                          ],
                        ),
                      ),
                    ),
            ),
            // Bottom progress bar
            _buildBottomProgressBar(),
          ],
        ),
      ),
    );
  }
}
