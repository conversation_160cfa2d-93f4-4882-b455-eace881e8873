import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as entities;
import 'package:storetrack_app/features/home/<USER>/services/photo_service.dart';
import 'package:storetrack_app/features/home/<USER>/widgets/photo_upload_widget.dart';

class QuestionCard extends StatefulWidget {
  final entities.Question question;
  final double progress;
  final String progressText;
  final bool isMandatory;
  final bool showCameraIcon;
  final bool isCameraMandatory;
  final bool hasPhotoUrl;
  final num? taskId;
  final num? formId;
  final VoidCallback? onQuestionTap;
  final VoidCallback? onPhotoSectionTap;
  final int? photoRefreshCounter;

  const QuestionCard({
    super.key,
    required this.question,
    required this.progress,
    required this.progressText,
    required this.isMandatory,
    required this.showCameraIcon,
    required this.isCameraMandatory,
    required this.hasPhotoUrl,
    this.taskId,
    this.formId,
    this.onQuestionTap,
    this.onPhotoSectionTap,
    this.photoRefreshCounter,
  });

  @override
  State<QuestionCard> createState() => _QuestionCardState();
}

class _QuestionCardState extends State<QuestionCard> {
  // Photo service for loading and managing photos
  late final PhotoService _photoService;

  // State management for photos for this question
  final List<String> _questionPhotos = [];

  @override
  void initState() {
    super.initState();
    _photoService = sl<PhotoService>();
    _loadSavedPhotos();
  }

  @override
  void didUpdateWidget(QuestionCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Reload photos if the question, task, or photoRefreshCounter changed
    if (oldWidget.question.questionId != widget.question.questionId ||
        oldWidget.taskId != widget.taskId ||
        oldWidget.photoRefreshCounter != widget.photoRefreshCounter) {
      _loadSavedPhotos();
    }
  }

  /// Load saved photos for this question with level 2 (combinetypeId = '2')
  Future<void> _loadSavedPhotos() async {
    if (widget.taskId == null || widget.question.questionId == null) {
      logger('TaskId or QuestionId is null, cannot load saved photos');
      return;
    }

    try {
      // Clear existing photo data
      _questionPhotos.clear();

      final taskId = widget.taskId!.toInt();
      final questionId = widget.question.questionId!.toInt();

      final savedPhotos = await _photoService.getPhotosFromTask(
        taskId: taskId,
        folderId: null,
      );

      // Filter photos for this question and level 2 (photo_tags_two)
      final filteredPhotos = savedPhotos.where((photo) {
        return photo.questionId == questionId &&
            photo.combineTypeId == 2; // Level 2 for photo_tags_two
      }).toList();

      if (filteredPhotos.isNotEmpty && mounted) {
        setState(() {
          // Add photo URLs to the list
          for (final photo in filteredPhotos) {
            if (photo.photoUrl != null) {
              _questionPhotos.add(photo.photoUrl!);
            }
          }
        });
      }

      logger(
          'Loaded ${filteredPhotos.length} photos for Question ${widget.question.questionId}');
      logger('Question photos: $_questionPhotos');
    } catch (e) {
      logger('Error loading saved photos: $e');
    }
  }

  /// Public method to refresh photos - can be called from parent widget
  Future<void> refreshPhotos() async {
    await _loadSavedPhotos();
  }

  /// Get the first PhotoTagsT object from photo_tags_two array for this question
  entities.PhotoTagsT? _getPhotoTagForQuestion() {
    if (widget.question.photoTagsTwo == null ||
        widget.question.photoTagsTwo!.isEmpty) {
      return null;
    }

    // Return the first photo tag from photo_tags_two array
    return widget.question.photoTagsTwo!.first;
  }

  @override
  Widget build(BuildContext context) {
    // Define text styles based on the image and typical app theming
    // Main Title Style (e.g., "Multipack Stickering Task")
    final titleTextStyle =
        Theme.of(context).textTheme.montserratTitleExtraSmall;

    // Progress Text Style (e.g., "0 of 5")
    final progressTextStyle = Theme.of(context).textTheme.montserratTableSmall;
    //  Theme.of(context).textTheme.bodyLarge?.copyWith(
    //   ---
    //           // bodyLarge is typically 16sp
    //           color: AppColors.blackTint1, // Use a grey color from AppColors
    //           fontWeight: FontWeight.w500,
    //           fontSize: 16, // As per image
    //         ) ??
    // TextStyle(
    //   // Fallback
    //   color: Colors.grey.shade600,
    //   fontWeight: FontWeight.w500,
    //   fontSize: 16,
    // );

    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(
                10.0), // Slightly more rounded corners for the main card
            boxShadow: [
              BoxShadow(
                color:
                    Colors.black.withValues(alpha: 0.05), // Light shadow color
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: InkWell(
            borderRadius:
                BorderRadius.circular(10.0), // Match container's border radius
            onTap: widget.onQuestionTap,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 1. Title (e.g., "Multipack Stickering Task")
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.question.questionDescription ??
                            'Unnamed Question',
                        style: titleTextStyle,
                      ),
                    ),
                    if (widget.isMandatory) ...[
                      const Gap(4),
                      Container(
                        width: 16,
                        height: 16,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.priority_high,
                          color: AppColors.loginRed,
                          size: 16,
                        ),
                      ),
                    ],
                    if (widget.hasPhotoUrl) ...[
                      const Gap(4),
                      Container(
                        width: 16,
                        height: 16,
                        decoration: const BoxDecoration(
                          color: Colors.blue,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.image,
                          color: Colors.white,
                          size: 10,
                        ),
                      ),
                    ],
                  ],
                ),
                const Gap(12), // Spacing after title

                // 2. Progress bar and text row (e.g., "0 of 5")
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 8, // Thicker progress bar
                        clipBehavior: Clip
                            .antiAlias, // Ensures rounded corners are respected by child
                        decoration: BoxDecoration(
                          color: AppColors.lightGrey2, // Background of the bar
                          borderRadius: BorderRadius.circular(
                              4), // Rounded ends for the bar
                        ),
                        child: FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: widget.progress.clamp(0.0, 1.0),
                          child: Container(
                            decoration: BoxDecoration(
                              color: AppColors.primaryBlue, // Progress color
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const Gap(16), // Space between bar and text
                    Text(
                      widget.progressText,
                      style: progressTextStyle,
                    ),
                  ],
                ),

                // Conditionally add space only if the "Add photos" section will be shown
                if (widget.showCameraIcon) const Gap(20),

                // 3. "Add photos" section (conditional based on showCameraIcon)
                // "Add photos" section
                if (widget.showCameraIcon) ...[
                  PhotoUploadWidget(
                    onCameraPressed: widget.onPhotoSectionTap,
                    onImagesTap: widget.onPhotoSectionTap,
                    selectedImages: _questionPhotos, // Use loaded photos
                    photoTag: _getPhotoTagForQuestion(),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }
}
